import { html } from "lit-html";
import "./progressGaugeCard.css";

/**
 * Renders a progress gauge card showing challenge completion status
 *
 * @param {Object} props - Component properties
 * @param {string} props.title - Card title
 * @param {string} props.description - Card description
 * @param {number} props.completedTrainings - Number of completed trainings
 * @param {number} props.totalTrainings - Total number of required trainings
 * @param {string} [props.backgroundColor="--cards-progress-gauge-card-background-success"] - Background color variable
 * @param {string} [props.textColor="--cards-progress-gauge-card-text"] - Text color variable
 * @param {string} [props.gaugeColor="#00B67A"] - Color of the gauge progress arc
 * @returns {TemplateResult} The progress gauge card template
 */
export const progressGaugeCard = ({
  title,
  description,
  completedTrainings,
  totalTrainings,
  backgroundColor = "--cards-progress-gauge-card-background-success",
  textColor = "--cards-progress-gauge-card-text",
  gaugeColor = "#00B67A"
}) => {
  // Calculate progress percentage
  const progressPercentage = totalTrainings > 0
    ? Math.round((completedTrainings / totalTrainings) * 100)
    : 0;

  // Calculate the stroke-dashoffset based on the percentage
  // The arc is approximately 270 degrees (3/4 of a circle)
  const circumference = 2 * Math.PI * 54; // Radius is 54 for the larger SVG
  const arcLength = circumference * 0.75; // 270 degrees (3/4 of a circle)
  const strokeDashoffset = arcLength - (progressPercentage / 100) * arcLength;

  return html`
    <div class="progressGaugeCard" style="background: var(${backgroundColor})">
      <div class="progressGaugeContainer">
        <div class="gaugeContainer">
          <svg width="132" height="105" viewBox="0 0 132 105" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- Background arc (white with shadow) -->
            <g filter="url(#filter0_d_gauge)">
              <path d="M22.3131 94.7405C19.6323 96.6882 15.8509 96.1067 14.1806 93.2448C9.63984 85.465 6.89574 76.7388 6.18497 67.7076C5.31678 56.6763 7.51605 45.6198 12.5396 35.7606C17.5632 25.9012 25.2153 17.6232 34.6501 11.8416C44.0849 6.05997 54.9347 3 66 3C77.0653 3 87.9151 6.05997 97.3499 11.8416C106.785 17.6232 114.437 25.9012 119.46 35.7606C124.484 45.6198 126.683 56.6763 125.815 67.7076C125.104 76.7388 122.36 85.465 117.819 93.2448C116.149 96.1067 112.368 96.6882 109.687 94.7405C107.006 92.7927 106.449 88.5547 108.047 86.152C111.327 80.1958 113.315 73.5897 113.852 66.766C114.547 57.9411 112.787 49.0959 108.768 41.2085C104.75 33.321 98.6277 26.6985 91.08 22.0732C83.5321 17.448 74.8522 15 66 15C57.1478 15 48.4679 17.448 40.92 22.0732C33.3723 26.6985 27.2503 33.321 23.2317 41.2085C19.213 49.0959 17.4534 57.9411 18.1479 66.766C18.6851 73.5897 20.673 80.1958 23.9526 86.152C25.5509 88.5547 24.9939 92.7927 22.3131 94.7405Z"
                fill="white"/>
            </g>

            <!-- Progress arc (colored) - using stroke-dasharray and stroke-dashoffset for progress -->
            <path class="progress-arc"
              d="M22.3131 94.7405C19.6323 96.6882 15.8509 96.1067 14.1806 93.2448C9.63984 85.465 6.89574 76.7388 6.18497 67.7076C5.31678 56.6763 7.51605 45.6198 12.5396 35.7606C17.5632 25.9012 25.2153 17.6232 34.6501 11.8416C44.0849 6.05997 54.9347 3 66 3C77.0653 3 87.9151 6.05997 97.3499 11.8416C106.785 17.6232 114.437 25.9012 119.46 35.7606C124.484 45.6198 126.683 56.6763 125.815 67.7076C125.104 76.7388 122.36 85.465 117.819 93.2448C116.149 96.1067 112.368 96.6882 109.687 94.7405"
              stroke="${gaugeColor}"
              stroke-width="12"
              fill="none"
              stroke-linecap="round"
              stroke-dasharray="${arcLength}"
              stroke-dashoffset="${strokeDashoffset}"
              style="opacity: ${progressPercentage === 0 ? 0.3 : 1}; transition: opacity 0.3s ease;"
            />

            <!-- Filter for drop shadow -->
            <defs>
              <filter id="filter0_d_gauge" x="0" y="0" width="132" height="104.88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                <feOffset dy="3"/>
                <feGaussianBlur stdDeviation="3"/>
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7428_31419"/>
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7428_31419" result="shape"/>
              </filter>
            </defs>
          </svg>

          <!-- Percentage text as HTML element -->
          <p class="percentage-text" style="color: var(${textColor})">
            ${progressPercentage} %
          </p>

          <p class="gaugeText" style="color: var(${textColor}); text-align: center; margin: 0; margin-top: 8px;">
            ${completedTrainings} von ${totalTrainings}<br>erledigt
          </p>
        </div>
      </div>
      <div class="contentGaugeContainer">
        <h3 class="gaugeTitle" style="color: var(${textColor})">${title}</h3>
        <p class="gaugeText" style="color: var(${textColor})">${description}</p>
      </div>
    </div>
  `;
};
